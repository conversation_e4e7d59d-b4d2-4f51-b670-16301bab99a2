#!/usr/bin/env node

/**
 * 构建验证脚本
 * 检查生产构建是否包含HMR相关代码
 */

const fs = require('fs');
const path = require('path');

const DIST_DIR = path.join(__dirname, '../dist');
const HMR_PATTERNS = [
  /ws:\/\/.*:5173/g,
  /wss:\/\/.*:5173/g,
  /WebSocket.*5173/g,
  /__vite__hmr/g,
  /import\.meta\.hot/g,
  /\[vite\]/g,
  /server connection lost/gi,
  /polling for restart/gi,
  /hmr/gi,
  /hot.*reload/gi,
];

function checkFile(filePath) {
  const content = fs.readFileSync(filePath, 'utf8');
  const issues = [];
  
  HMR_PATTERNS.forEach((pattern, index) => {
    const matches = content.match(pattern);
    if (matches) {
      issues.push({
        pattern: pattern.toString(),
        matches: matches.length,
        examples: matches.slice(0, 3) // 只显示前3个匹配
      });
    }
  });
  
  return issues;
}

function scanDirectory(dir) {
  const results = [];
  
  function scan(currentDir) {
    const items = fs.readdirSync(currentDir);
    
    items.forEach(item => {
      const itemPath = path.join(currentDir, item);
      const stat = fs.statSync(itemPath);
      
      if (stat.isDirectory()) {
        scan(itemPath);
      } else if (stat.isFile() && (item.endsWith('.js') || item.endsWith('.css') || item.endsWith('.html'))) {
        const issues = checkFile(itemPath);
        if (issues.length > 0) {
          results.push({
            file: path.relative(DIST_DIR, itemPath),
            issues
          });
        }
      }
    });
  }
  
  scan(dir);
  return results;
}

function main() {
  console.log('🔍 开始验证生产构建...');
  console.log(`📁 扫描目录: ${DIST_DIR}`);
  
  if (!fs.existsSync(DIST_DIR)) {
    console.error('❌ 构建目录不存在，请先运行构建命令');
    process.exit(1);
  }
  
  const results = scanDirectory(DIST_DIR);
  
  if (results.length === 0) {
    console.log('✅ 验证通过！生产构建中未发现HMR相关代码');
    console.log('🎉 可以安全部署到生产环境');
  } else {
    console.error('❌ 验证失败！发现以下HMR相关代码:');
    console.error('');
    
    results.forEach(result => {
      console.error(`📄 文件: ${result.file}`);
      result.issues.forEach(issue => {
        console.error(`   🔍 模式: ${issue.pattern}`);
        console.error(`   📊 匹配数: ${issue.matches}`);
        console.error(`   📝 示例: ${issue.examples.join(', ')}`);
        console.error('');
      });
    });
    
    console.error('⚠️  请检查Vite配置并重新构建');
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}
