import type { Plugin } from 'vite';

/**
 * 自定义Vite插件：生产环境移除HMR客户端代码
 * 
 * 这个插件确保在生产构建中完全移除所有HMR相关的代码，
 * 包括WebSocket连接、客户端代码和相关的全局变量。
 */
export function removeHmrPlugin(): Plugin {
  return {
    name: 'remove-hmr-plugin',
    apply: 'build', // 只在构建时应用
    config(config, { mode }) {
      const isProduction = mode === 'production';
      
      if (isProduction) {
        console.log('🚫 RemoveHmrPlugin: 生产环境已激活，将移除所有HMR相关代码');
        
        // 确保define配置存在
        config.define = config.define || {};
        
        // 强制禁用HMR相关的全局变量
        Object.assign(config.define, {
          'import.meta.hot': 'undefined',
          '__vite__hmr': 'undefined',
          '__vite__client': 'undefined',
          'import.meta.env.DEV': 'false',
          'import.meta.env.PROD': 'true',
        });
        
        // 确保server配置不会影响构建
        if (config.server) {
          config.server.hmr = false;
        }
      }
    },
    
    generateBundle(options, bundle) {
      // 在生成bundle时检查并移除HMR相关代码
      Object.keys(bundle).forEach(fileName => {
        const chunk = bundle[fileName];
        
        if (chunk.type === 'chunk' && chunk.code) {
          // 检查是否包含HMR相关代码
          const hmrPatterns = [
            /ws:\/\/.*:5173/g,
            /wss:\/\/.*:5173/g,
            /WebSocket.*5173/g,
            /__vite__hmr/g,
            /import\.meta\.hot/g,
            /\[vite\]/g,
            /server connection lost/g,
            /Polling for restart/g,
          ];
          
          let hasHmrCode = false;
          let originalCode = chunk.code;
          
          hmrPatterns.forEach(pattern => {
            if (pattern.test(chunk.code)) {
              hasHmrCode = true;
              console.warn(`⚠️ RemoveHmrPlugin: 在 ${fileName} 中发现HMR代码模式: ${pattern}`);
            }
          });
          
          if (hasHmrCode) {
            console.error(`❌ RemoveHmrPlugin: 文件 ${fileName} 仍包含HMR代码，这不应该发生在生产构建中！`);
            
            // 尝试移除HMR相关代码（作为最后的保护措施）
            hmrPatterns.forEach(pattern => {
              chunk.code = chunk.code.replace(pattern, '');
            });
            
            if (chunk.code !== originalCode) {
              console.log(`🔧 RemoveHmrPlugin: 已从 ${fileName} 中移除HMR代码`);
            }
          }
        }
      });
    },
    
    writeBundle() {
      console.log('✅ RemoveHmrPlugin: 生产构建完成，HMR代码检查已完成');
    }
  };
}
