import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import path from 'path';

export default defineConfig(({ mode }) => {
  const isProduction = mode === 'production';

  return {
    plugins: [react()],
    optimizeDeps: {
      exclude: ['lucide-react'],
    },
    resolve: {
      alias: {
        '@': path.resolve(__dirname, './src'),
        '@new-mianshijun/common': path.resolve(__dirname, '../packages/common/src'),
      },
    },
    server: {
      host: true,
      port: 5173,
      strictPort: true,
      allowedHosts: [
        'localhost',
        '127.0.0.1',
        'mianshijun.xyz',
        'www.mianshijun.xyz',
        '*************'
      ],
      hmr: isProduction ? false : {
        // 开发环境使用本地HMR
        port: 5173
      },
      proxy: {
        '/api': {
          target: 'http://localhost:3000',
          changeOrigin: true,
          secure: false,
        },
      },
    },
    build: {
      // 生产环境使用默认压缩（esbuild）
      minify: isProduction ? 'esbuild' : false,
      rollupOptions: {
        output: {
          manualChunks: undefined,
        },
      },
    },
    // 生产环境定义环境变量，确保客户端代码知道这是生产环境
    define: {
      __DEV__: !isProduction,
      'process.env.NODE_ENV': JSON.stringify(mode),
    },
  };
});
