import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import path from 'path';
import { removeHmrPlugin } from './vite-plugins/remove-hmr-plugin';

export default defineConfig(({ mode }) => {
  const isProduction = mode === 'production';

  console.log(`🔧 Vite配置: mode=${mode}, isProduction=${isProduction}`);

  return {
    plugins: [
      react(),
      // 生产环境移除HMR代码的自定义插件
      ...(isProduction ? [removeHmrPlugin()] : [])
    ],
    optimizeDeps: {
      exclude: ['lucide-react'],
    },
    resolve: {
      alias: {
        '@': path.resolve(__dirname, './src'),
        '@new-mianshijun/common': path.resolve(__dirname, '../packages/common/src'),
      },
    },
    server: {
      host: true,
      port: 5173,
      strictPort: true,
      allowedHosts: [
        'localhost',
        '127.0.0.1',
        'mianshijun.xyz',
        'www.mianshijun.xyz',
        '*************'
      ],
      // 开发环境HMR配置
      hmr: isProduction ? false : {
        port: 5173,
        host: 'localhost'
      },
      proxy: {
        '/api': {
          target: 'http://localhost:3000',
          changeOrigin: true,
          secure: false,
        },
      },
    },
    build: {
      // 生产环境构建配置
      minify: isProduction ? 'esbuild' : false,
      sourcemap: false, // 生产环境不生成sourcemap
      rollupOptions: {
        output: {
          manualChunks: undefined,
        },
        // 生产环境排除开发依赖
        external: isProduction ? [] : undefined,
      },
      // 确保生产环境不包含开发代码
      target: isProduction ? 'es2015' : 'esnext',
    },
    // 环境变量定义 - 关键配置
    define: {
      __DEV__: JSON.stringify(!isProduction),
      'process.env.NODE_ENV': JSON.stringify(mode),
      // 生产环境禁用HMR相关全局变量
      'import.meta.hot': isProduction ? 'undefined' : 'import.meta.hot',
      'import.meta.env.DEV': JSON.stringify(!isProduction),
      'import.meta.env.PROD': JSON.stringify(isProduction),
    },
    // 生产环境禁用开发工具
    esbuild: isProduction ? {
      drop: ['console', 'debugger'], // 生产环境移除console和debugger
    } : undefined,
  };
});
